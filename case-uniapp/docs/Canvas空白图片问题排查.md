# Canvas空白图片问题排查指南

## 问题现象

生成的海报是空白图片，没有任何内容显示。

## 常见原因及解决方案

### 1. Canvas尺寸问题

**原因**: Canvas尺寸设置不正确或过大
**解决方案**:
```javascript
// 确保Canvas尺寸合理
const canvasWidth = 750  // 标准宽度
const canvasHeight = Math.min(2000, Math.max(1334, container.height * 2))

// 在canvasToTempFilePath中指定尺寸
uni.canvasToTempFilePath({
  canvasId: 'posterCanvas',
  width: canvasWidth,
  height: canvasHeight,
  destWidth: canvasWidth,    // 添加这个
  destHeight: canvasHeight,  // 添加这个
  success: (res) => { ... }
}, this)
```

### 2. 绘制时机问题

**原因**: 在图片或DOM未加载完成时就开始绘制
**解决方案**:
```javascript
// 等待DOM渲染
await this.$nextTick()

// 等待图片加载
await this.waitForImagesLoad()

// 增加绘制延迟
ctx.draw(false, () => {
  setTimeout(() => {
    uni.canvasToTempFilePath({ ... }, this)
  }, 2000) // 增加延迟时间
})
```

### 3. 图片资源加载失败

**原因**: 网络图片下载失败或路径错误
**解决方案**:
```javascript
// 预加载所有图片
async waitForImagesLoad() {
  const imagesToLoad = []
  
  // 添加用户头像
  if (this.userInfo && this.userInfo.avatar) {
    imagesToLoad.push(this.userInfo.avatar)
  }
  
  // 添加案例图片
  if (this.images && this.images.length > 0) {
    imagesToLoad.push(...this.images)
  }
  
  // 批量下载
  for (const url of imagesToLoad) {
    try {
      await imageHelper.downloadImage(url)
    } catch (error) {
      console.warn('图片预加载失败:', url, error)
    }
  }
}
```

### 4. DOM查询失败

**原因**: DOM元素未找到或查询时机不对
**解决方案**:
```javascript
// 确保DOM元素存在
analyzeDOMStructure() {
  return new Promise((resolve, reject) => {
    const query = uni.createSelectorQuery().in(this)
    
    // 添加容器查询
    query.select(`#poster-preview-${this.caseId}`).boundingClientRect()
    
    query.exec((res) => {
      if (res && res[0] && res[0].width > 0) {
        resolve(res)
      } else {
        console.error('DOM查询结果:', res)
        reject(new Error('DOM元素未找到或尺寸为0'))
      }
    })
  })
}
```

### 5. Canvas上下文问题

**原因**: Canvas上下文创建失败或使用错误
**解决方案**:
```javascript
// 确保Canvas ID正确且唯一
const ctx = uni.createCanvasContext('posterCanvas', this)

// 检查上下文是否创建成功
if (!ctx) {
  throw new Error('Canvas上下文创建失败')
}

// 清空Canvas并设置背景
ctx.clearRect(0, 0, width, height)
ctx.setFillStyle('#ffffff')
ctx.fillRect(0, 0, width, height)
```

### 6. 平台兼容性问题

**原因**: 不同平台的Canvas实现差异
**解决方案**:
```javascript
// H5环境特殊处理
// #ifdef H5
// H5环境下的特殊逻辑
// #endif

// 小程序环境
// #ifdef MP-WEIXIN
// 微信小程序特殊逻辑
// #endif

// App环境
// #ifdef APP-PLUS
// App环境特殊逻辑
// #endif
```

## 调试步骤

### 1. 使用调试工具

```javascript
import { debugCanvas } from '@/utils/canvasDebugger.js'

// 运行Canvas诊断
const diagnosis = await debugCanvas('posterCanvas', this)
console.log('诊断结果:', diagnosis)
```

### 2. 添加调试信息

```javascript
// 在Canvas上绘制调试信息
ctx.setFillStyle('#ff0000')
ctx.setFontSize(24)
ctx.fillText('调试: Canvas正在工作', 50, 50)

// 绘制边框确认Canvas尺寸
ctx.setStrokeStyle('#00ff00')
ctx.setLineWidth(2)
ctx.strokeRect(0, 0, width, height)
```

### 3. 检查控制台日志

关注以下关键日志:
- Canvas上下文创建
- DOM查询结果
- 图片下载状态
- 绘制操作执行
- canvasToTempFilePath结果

### 4. 简化测试

先用简单的Canvas绘制测试:
```javascript
generateSimplePoster() {
  const ctx = uni.createCanvasContext('posterCanvas', this)
  
  // 绘制简单内容
  ctx.setFillStyle('#ffffff')
  ctx.fillRect(0, 0, 750, 1334)
  
  ctx.setFillStyle('#333333')
  ctx.setFontSize(48)
  ctx.setTextAlign('center')
  ctx.fillText('测试海报', 375, 200)
  
  ctx.draw(false, () => {
    setTimeout(() => {
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        width: 750,
        height: 1334,
        success: (res) => console.log('简单测试成功:', res),
        fail: (error) => console.error('简单测试失败:', error)
      }, this)
    }, 1000)
  })
}
```

## 最佳实践

1. **分步调试**: 先确保简单绘制正常，再逐步添加复杂内容
2. **错误处理**: 为每个异步操作添加try-catch
3. **日志记录**: 详细记录每个步骤的执行情况
4. **资源预加载**: 确保所有图片资源都已下载
5. **延迟处理**: 给Canvas绘制足够的时间
6. **平台测试**: 在不同平台上测试功能

## 常用调试代码

```javascript
// 检查Canvas是否可用
console.log('Canvas API可用性:', {
  createCanvasContext: typeof uni.createCanvasContext === 'function',
  canvasToTempFilePath: typeof uni.canvasToTempFilePath === 'function'
})

// 检查DOM元素
const query = uni.createSelectorQuery().in(this)
query.select('#poster-preview').boundingClientRect()
query.exec((res) => {
  console.log('DOM查询结果:', res)
})

// 检查图片下载
uni.downloadFile({
  url: 'your-image-url',
  success: (res) => console.log('图片下载成功:', res),
  fail: (error) => console.error('图片下载失败:', error)
})
```

通过以上步骤，应该能够定位并解决Canvas空白图片的问题。
