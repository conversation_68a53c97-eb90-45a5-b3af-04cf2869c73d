# 海报保存功能说明

## 功能概述

本项目实现了跨平台的海报生成和保存功能，支持在不同环境下将生成的海报保存到本地：

- **H5环境**: 触发浏览器下载
- **小程序环境**: 保存到相册（需要用户授权）
- **App环境**: 保存到相册

## 核心文件

### 1. 平台兼容保存工具 (`utils/platformSaveImage.js`)

这是核心的跨平台保存工具，自动检测当前运行环境并使用相应的保存方式。

```javascript
import { saveImageToLocal, showSaveResult } from '@/utils/platformSaveImage.js'

// 保存图片
try {
  const result = await saveImageToLocal('/path/to/image.png', {
    filename: 'my_poster.png'
  })
  showSaveResult(true, '保存成功')
} catch (error) {
  showSaveResult(false, error.message)
}
```

### 2. 海报生成组件 (`components/PosterGenerator.vue`)

负责生成海报并提供保存功能的Vue组件。

```vue
<template>
  <PosterGenerator 
    ref="posterGenerator"
    :visible="showPosterModal"
    :caseId="caseId"
    :userInfo="userInfo"
    :title="title"
    :content="content"
    @save-success="onSaveSuccess"
    @save-error="onSaveError"
  />
</template>
```

### 3. 案例详情页 (`pages/case/detail.vue`)

集成了海报生成和保存功能的案例详情页面。

## 使用方法

### 在案例详情页使用

1. 点击"生成海报"按钮打开海报弹窗
2. 在弹窗中点击"保存到相册"按钮
3. 系统会自动：
   - 生成海报图片
   - 根据当前平台选择保存方式
   - 处理权限申请（小程序/App）
   - 显示保存结果

### 在其他页面使用

```javascript
// 1. 引入保存工具
import { saveImageToLocal, showSaveResult } from '@/utils/platformSaveImage.js'

// 2. 保存图片
async function saveImage(imagePath) {
  try {
    uni.showLoading({ title: '保存中...' })
    
    const result = await saveImageToLocal(imagePath, {
      filename: `image_${Date.now()}.png`
    })
    
    uni.hideLoading()
    showSaveResult(true, '保存成功')
    console.log('保存结果:', result)
    
  } catch (error) {
    uni.hideLoading()
    showSaveResult(false, error.message)
    console.error('保存失败:', error)
  }
}
```

## 平台差异处理

### H5环境
- 使用 `<a>` 标签的 `download` 属性触发下载
- 图片会下载到浏览器的默认下载目录
- 不需要权限申请

### 小程序环境
- 使用 `uni.saveImageToPhotosAlbum` API
- 需要申请 `scope.writePhotosAlbum` 权限
- 自动处理权限申请和设置引导

### App环境
- 使用 `uni.saveImageToPhotosAlbum` API
- 在 `manifest.json` 中配置相册权限
- 支持Android和iOS

## 权限配置

### manifest.json 配置

```json
{
  "app-plus": {
    "distribute": {
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
          "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>"
        ]
      }
    }
  }
}
```

### 小程序权限

小程序会自动处理以下权限流程：
1. 检查是否已授权 `scope.writePhotosAlbum`
2. 未授权时自动申请权限
3. 权限被拒绝时引导用户到设置页面

## 错误处理

常见错误及解决方案：

1. **"当前环境不支持保存图片"**
   - 检查uni-app版本和平台支持
   - 确认API可用性

2. **"没有相册权限"**
   - 小程序：引导用户到设置页面开启权限
   - App：检查manifest.json权限配置

3. **"图片生成失败"**
   - 检查Canvas绘制过程
   - 确认图片资源可访问

4. **"用户取消保存"**
   - 用户主动取消操作，属于正常流程

## 测试页面

项目包含测试页面 `pages/test/saveImage.vue`，可以用来测试保存功能：

1. 测试保存网络图片
2. 测试生成并保存Canvas图片
3. 查看详细的操作日志

## 注意事项

1. **图片路径**: 确保图片路径正确且可访问
2. **网络图片**: 需要先下载到本地临时目录
3. **Canvas图片**: 使用 `uni.canvasToTempFilePath` 生成临时文件
4. **权限处理**: 小程序和App需要处理相册权限
5. **错误提示**: 提供友好的用户提示信息

## 开发建议

1. 在保存前显示加载提示
2. 保存后给用户明确的成功/失败反馈
3. 处理各种异常情况
4. 在不同平台上进行充分测试
5. 考虑用户体验，提供清晰的操作指引
